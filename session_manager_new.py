"""
Enhanced Session Manager with Bedrock Integration
Provides robust session management with AWS Bedrock Agent Runtime support
"""

import os
import logging
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional
from botocore.exceptions import ClientError
from uuid import uuid4

# Configure logging
logger = logging.getLogger(__name__)


class ConversationTurn:
    """Represents a single conversation turn with user message and assistant response."""

    def __init__(
        self,
        timestamp: datetime,
        user_message: str,
        assistant_response: str,
        tools_used: Optional[List[Dict[str, Any]]] = None,
        session_id: Optional[str] = None,
    ):
        self.timestamp = timestamp
        self.user_message = user_message
        self.assistant_response = assistant_response
        self.tools_used = tools_used or []
        self.session_id = session_id

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_message": self.user_message,
            "assistant_response": self.assistant_response,
            "tools_used": self.tools_used,
            "session_id": self.session_id,
        }


class SessionConfig:
    """Configuration for session management."""

    def __init__(
        self,
        region_name: Optional[str] = None,
        encryption_key_arn: Optional[str] = None,
        session_timeout_hours: int = 2,
        max_conversation_turns: int = 200,
        max_tools_per_turn: int = 25,
        enable_cleanup: bool = True,
        delete_on_cleanup: bool = False,
        recovery_max_steps: int = 200,
        compress_keep_recent: int = 20,
        max_user_message_len: int = 20000,
        max_assistant_response_len: int = 20000,
        max_metadata_len: int = 5000,
        persist_tools_minimal: bool = True,
    ):
        self.region_name = region_name or os.getenv("AWS_REGION", "us-east-1")
        self.encryption_key_arn = encryption_key_arn
        self.session_timeout_hours = session_timeout_hours
        self.max_conversation_turns = max_conversation_turns
        self.max_tools_per_turn = max_tools_per_turn
        self.enable_cleanup = enable_cleanup
        self.delete_on_cleanup = delete_on_cleanup
        self.recovery_max_steps = recovery_max_steps
        self.compress_keep_recent = compress_keep_recent
        self.max_user_message_len = max_user_message_len
        self.max_assistant_response_len = max_assistant_response_len
        self.max_metadata_len = max_metadata_len
        self.persist_tools_minimal = persist_tools_minimal

    def validate(self):
        """Validate configuration values."""
        if self.session_timeout_hours <= 0:
            raise ValueError("session_timeout_hours must be positive")
        if self.max_conversation_turns <= 0:
            raise ValueError("max_conversation_turns must be positive")
        if self.max_tools_per_turn <= 0:
            raise ValueError("max_tools_per_turn must be positive")


class BedrockSessionClient:
    """Simplified AWS Bedrock Agent Runtime client for session management."""

    def __init__(
        self,
        region_name: str,
        encryption_key_arn: Optional[str] = None,
        session_metadata: Optional[Dict[str, str]] = None,
    ):
        self.region_name = region_name
        self.encryption_key_arn = encryption_key_arn
        self.session_metadata = session_metadata or {}

        # Initialize Bedrock Agent Runtime client
        try:
            import boto3
            self.client = boto3.client(
                'bedrock-agent-runtime',
                region_name=region_name
            )
            logger.info(f"Bedrock Agent Runtime client initialized for region {region_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock Agent Runtime client: {e}")
            raise

    def get_session(self, session_identifier: str) -> Dict[str, Any]:
        """Get a Bedrock session."""
        try:
            response = self.client.get_session(sessionIdentifier=session_identifier)
            logger.debug(f"Retrieved Bedrock session: {session_identifier}")
            return response
        except Exception as e:
            logger.error(f"Failed to get Bedrock session {session_identifier}: {e}")
            raise

    def create_session(self) -> str:
        """Create a new Bedrock session."""
        try:
            response = self.client.create_session(
                sessionMetadata=self.session_metadata,
                encryptionKeyArn=self.encryption_key_arn
            )
            session_id = response['sessionId']
            logger.info(f"Created Bedrock session: {session_id}")
            return session_id
        except Exception as e:
            logger.error(f"Failed to create Bedrock session: {e}")
            raise

    def delete_session(self, session_id: str):
        """Delete a Bedrock session."""
        try:
            self.client.delete_session(sessionIdentifier=session_id)
            logger.info(f"Deleted Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to delete Bedrock session {session_id}: {e}")
            raise

    def end_session(self, session_id: str):
        """End a Bedrock session."""
        try:
            self.client.end_session(sessionIdentifier=session_id)
            logger.info(f"Ended Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to end Bedrock session {session_id}: {e}")
            raise

    def create_invocation(self, session_identifier: str, description: str) -> str:
        """Create an invocation within a session."""
        try:
            response = self.client.create_invocation(
                sessionIdentifier=session_identifier,
                description=description
            )
            invocation_id = response['invocationId']
            logger.info(f"Created invocation {invocation_id} for session {session_identifier}")
            return invocation_id
        except Exception as e:
            logger.error(f"Failed to create invocation for session {session_identifier}: {e}")
            raise

    def put_invocation_step_structured(
        self,
        session_identifier: str,
        invocation_identifier: str,
        role: str,
        text: str,
        tools_used: List[Dict[str, Any]],
        timestamp: datetime,
        persist_tools_minimal: bool,
        max_metadata_len: int,
    ):
        """Put a structured invocation step with contentBlocks payload."""
        try:
            # Build contentBlocks payload (tagged union)
            blocks: List[Dict[str, Any]] = []
            if text:
                blocks.append({"text": text})

            # Add metadata as a text block
            meta = {"role": role, "timestamp": timestamp.isoformat()}
            meta_s = json.dumps(meta)
            if len(meta_s) <= max_metadata_len:
                blocks.append({"text": f"[METADATA]{meta_s}"})

            # Add tools as a text block if needed
            if tools_used and not persist_tools_minimal:
                tools_s = json.dumps({"tools": tools_used})
                if len(tools_s) <= max_metadata_len:
                    blocks.append({"text": f"[TOOLS]{tools_s}"})

            payload = {"contentBlocks": blocks}

            self.client.put_invocation_step(
                sessionIdentifier=session_identifier,
                invocationIdentifier=invocation_identifier,
                invocationStepId=str(uuid4()),
                invocationStepTime=timestamp,
                payload=payload
            )
            logger.debug(f"Put invocation step for session {session_identifier}")
        except Exception as e:
            logger.error(f"Failed to put invocation step: {e}")
            raise

    def list_invocation_steps(
        self,
        session_identifier: str,
        max_steps: int = 50,
        next_token: Optional[str] = None
    ) -> tuple[List[Dict[str, Any]], Optional[str]]:
        """List invocation steps for a session."""
        try:
            params: Dict[str, Any] = {
                "sessionIdentifier": session_identifier,
                "maxResults": max_steps
            }
            if next_token:
                params["nextToken"] = next_token

            response = self.client.list_invocation_steps(**params)
            steps = response.get('invocationStepSummaries', [])
            next_token = response.get('nextToken')

            logger.debug(f"Listed {len(steps)} invocation steps for session {session_identifier}")
            return steps, next_token
        except Exception as e:
            logger.error(f"Failed to list invocation steps for session {session_identifier}: {e}")
            return [], None

    def get_invocation_step(
        self,
        session_identifier: str,
        invocation_identifier: str,
        invocation_step_id: str
    ) -> Dict[str, Any]:
        """Get a specific invocation step."""
        try:
            response = self.client.get_invocation_step(
                sessionIdentifier=session_identifier,
                invocationIdentifier=invocation_identifier,
                invocationStepId=invocation_step_id
            )
            payload = response.get('payload', {})
            logger.debug(f"Retrieved invocation step {invocation_step_id}")
            return payload
        except Exception as e:
            logger.error(f"Failed to get invocation step {invocation_step_id}: {e}")
            return {}


class ChatSession:
    """
    Manages individual chat session with conversation history and context,
    persisted via Bedrock sessions and locally cached for quick access.
    """
    def __init__(
        self,
        session_id: Optional[str] = None,
        bedrock_backend: Optional['BedrockSessionClient'] = None,
        invocation_description: Optional[str] = "mcp-bot conversation",
        config: Optional['SessionConfig'] = None,
    ):
        self.config = config or SessionConfig()
        self.config.validate()

        # Try to use Bedrock session management, fallback to local-only if not available
        try:
            self.bedrock = bedrock_backend or BedrockSessionClient(
                region_name=self.config.region_name,
                encryption_key_arn=self.config.encryption_key_arn
            )
            self.session_id = session_id or self.bedrock.create_session()
            self.use_bedrock_sessions = True
            logger.info(f"Using Bedrock session management for session {self.session_id}")
        except Exception as e:
            logger.warning(f"Bedrock session management not available, using local-only mode: {e}")
            self.bedrock = None
            # local session id
            from uuid import uuid4 as _uuid4
            self.session_id = session_id or f"local_{str(_uuid4())[:8]}"
            self.use_bedrock_sessions = False

        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)

        self.conversation_history: List[ConversationTurn] = []
        self.context_summary = ""
        self.total_tools_used = 0
        self._last_summary_turn_count = 0

        # Create an invocation group only if Bedrock sessions are enabled
        if self.use_bedrock_sessions:
            try:
                self.current_invocation_id: Optional[str] = self.bedrock.create_invocation(
                    session_identifier=self.session_id,
                    description=invocation_description
                )
            except Exception as e:
                logger.warning(f"Failed to create invocation for session {self.session_id}: {e}")
                self.current_invocation_id = None
        else:
            self.current_invocation_id = None

        # Attempt to warm start from Bedrock (optional)
        try:
            self.load_from_bedrock(max_steps=self.config.recovery_max_steps)
        except Exception as e:
            logger.warning(f"Warm recovery skipped for session {self.session_id}: {e}")

    def _ensure_invocation(self):
        if not self.use_bedrock_sessions:
            return
        if not self.current_invocation_id:
            try:
                self.current_invocation_id = self.bedrock.create_invocation(
                    session_identifier=self.session_id,
                    description="mcp-bot conversation"
                )
            except Exception as e:
                logger.warning(f"Failed to ensure invocation for session {self.session_id}: {e}")
                self.current_invocation_id = None

    def add_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]] = None):
        """Add a conversation turn with context tracking; persist to Bedrock steps if enabled."""
        self._validate_turn(user_message, assistant_response, tools_used)
        tools_used = tools_used or []
        now = datetime.now(timezone.utc)

        turn = ConversationTurn(
            timestamp=now,
            user_message=user_message,
            assistant_response=assistant_response,
            tools_used=tools_used,
            session_id=self.session_id
        )
        self.conversation_history.append(turn)
        if len(self.conversation_history) > self.config.max_conversation_turns:
            self.compress_old_turns(keep_recent=self.config.compress_keep_recent)

        self.last_activity = now
        self.total_tools_used += len(tools_used)

        if self.use_bedrock_sessions:
            self._ensure_invocation()
            if self.current_invocation_id:
                try:
                    self.bedrock.put_invocation_step_structured(
                        session_identifier=self.session_id,
                        invocation_identifier=self.current_invocation_id,
                        role="user",
                        text=user_message,
                        tools_used=[],
                        timestamp=now,
                        persist_tools_minimal=False,
                        max_metadata_len=self.config.max_metadata_len,
                    )
                    self.bedrock.put_invocation_step_structured(
                        session_identifier=self.session_id,
                        invocation_identifier=self.current_invocation_id,
                        role="assistant",
                        text=assistant_response,
                        tools_used=tools_used,
                        timestamp=now,
                        persist_tools_minimal=self.config.persist_tools_minimal,
                        max_metadata_len=self.config.max_metadata_len,
                    )
                except ClientError as e:
                    logger.warning(f"Bedrock step persistence failed for session {self.session_id}: {e}")
                except Exception as e:
                    logger.warning(f"Bedrock step persistence error for session {self.session_id}: {e}")

        self._update_context_summary_optimized()
        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")

    def load_from_bedrock(self, max_steps: int = 50) -> None:
        """Recover conversation history from Bedrock session if enabled."""
        if not self.use_bedrock_sessions or not self.bedrock:
            return

        collected: List[Dict[str, Any]] = []
        next_token: Optional[str] = None

        while len(collected) < max_steps:
            batch, next_token = self.bedrock.list_invocation_steps(
                session_identifier=self.session_id,
                max_steps=max_steps - len(collected),
                next_token=next_token
            )
            if not batch:
                break
            collected.extend(batch)
            if not next_token:
                break

        temp_turns: Dict[str, Dict[str, Any]] = {}
        for s in collected:
            inv_id = s.get("invocationIdentifier") or s.get("invocationId")
            step_id = s.get("invocationStepId")
            step_time = s.get("invocationStepTime")
            if not inv_id or not step_id:
                continue
            try:
                detail = self.bedrock.get_invocation_step(
                    session_identifier=self.session_id,
                    invocation_identifier=inv_id,
                    invocation_step_id=step_id,
                )
            except ClientError as e:
                logger.warning(f"Failed to get step payload for {step_id}: {e}")
                continue
            except Exception as e:
                logger.warning(f"Error retrieving step payload for {step_id}: {e}")
                continue

            parsed = self._parse_step_data(detail, fallback_time=step_time)
            if not parsed:
                continue

            turn_key = parsed.get('turn_id') or parsed.get('timestamp') or step_time
            if turn_key not in temp_turns:
                temp_turns[turn_key] = {"timestamp": parsed.get('timestamp')}
            role = parsed.get('role')
            if role == "user":
                temp_turns[turn_key]['user'] = parsed
            elif role == "assistant":
                temp_turns[turn_key]['assistant'] = parsed

        recovered = 0
        sorted_turns = sorted(temp_turns.values(), key=lambda x: x.get('timestamp', ''))
        for t in sorted_turns:
            if 'user' in t and 'assistant' in t:
                try:
                    ts = datetime.fromisoformat((t.get('timestamp') or '').replace('Z', '+00:00'))
                except Exception:
                    ts = datetime.now(timezone.utc)
                self.conversation_history.append(ConversationTurn(
                    timestamp=ts,
                    user_message=t['user'].get('text', ''),
                    assistant_response=t['assistant'].get('text', ''),
                    tools_used=t['assistant'].get('tools_used', []),
                    session_id=self.session_id
                ))
                recovered += 1

        if recovered:
            self._update_context_summary_optimized()
            logger.info(f"Recovered {recovered} turns from Bedrock")

    def get_bedrock_messages(self, max_turns: int = 8) -> List[Dict[str, Any]]:
        """Convert conversation history to Bedrock message format."""
        messages = []
        recent_turns = self.conversation_history[-max_turns:] if max_turns > 0 else self.conversation_history

        for turn in recent_turns:
            # Add user message
            messages.append({
                "role": "user",
                "content": [{"text": turn.user_message}]
            })

            # Add assistant message
            messages.append({
                "role": "assistant",
                "content": [{"text": turn.assistant_response}]
            })

        return messages

    def get_context_for_bedrock(self) -> str:
        """Compose a concise session context summary for system prompt."""
        parts: List[str] = []
        if hasattr(self, "created_at"):
            parts.append(f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}")
        parts.append(f"Total conversation turns: {len(self.conversation_history)}")
        parts.append(f"Total tools executed: {self.total_tools_used}")
        if getattr(self, "context_summary", ""):
            parts.append(f"Recent context: {self.context_summary}")

        # Recent successful tool names (if any recorded)
        recent_tools: List[str] = []
        recent_servers: set[str] = set()
        for turn in self.conversation_history[-3:]:
            for t in turn.tools_used:
                if t.get("success"):
                    if t.get("tool_name"):
                        recent_tools.append(t["tool_name"])
                    if t.get("server_name"):
                        recent_servers.add(t["server_name"])
        if recent_tools:
            parts.append(f"Recently used tools: {', '.join(sorted(set(recent_tools)))}")
        if recent_servers:
            parts.append(f"Active servers: {', '.join(sorted(recent_servers))}")
        return "\n".join(parts)

    def get_session_stats(self) -> Dict[str, Any]:
        """Get detailed statistics for this session."""
        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "context_summary_length": len(self.context_summary),
            "use_bedrock_sessions": self.use_bedrock_sessions,
            "current_invocation_id": self.current_invocation_id,
            "session_age_hours": (datetime.now(timezone.utc) - self.created_at).total_seconds() / 3600,
            "inactive_minutes": (datetime.now(timezone.utc) - self.last_activity).total_seconds() / 60
        }

    def _validate_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]] = None):
        """Validate turn data before adding to conversation history."""
        if not user_message or not isinstance(user_message, str):
            raise ValueError("User message must be a non-empty string")
        if not assistant_response or not isinstance(assistant_response, str):
            raise ValueError("Assistant response must be a non-empty string")
        if len(user_message) > self.config.max_user_message_len:
            raise ValueError(f"User message too long: {len(user_message)} > {self.config.max_user_message_len}")
        if len(assistant_response) > self.config.max_assistant_response_len:
            raise ValueError(f"Assistant response too long: {len(assistant_response)} > {self.config.max_assistant_response_len}")
        if tools_used and len(tools_used) > self.config.max_tools_per_turn:
            raise ValueError(f"Too many tools used: {len(tools_used)} > {self.config.max_tools_per_turn}")

    def _extract_topics_fast(self, text: str) -> List[str]:
        """Extract topics from text using keyword matching."""
        topics: List[str] = []
        low = text.lower()
        if any(k in low for k in ("budget", "cost", "billing")):
            topics.append("cost_analysis")
        if any(k in low for k in ("cloudformation", "stack", "template")):
            topics.append("infrastructure")
        if any(k in low for k in ("pricing", "price", "compare")):
            topics.append("pricing_analysis")
        if any(k in low for k in ("bedrock", "claude", "model")):
            topics.append("ai_services")
        return topics

    def _update_context_summary_optimized(self):
        """Lightweight rolling summary of the last few turns."""
        if len(self.conversation_history) < 2:
            return
        recent = self.conversation_history[-3:]
        topics = set()
        for t in recent:
            topics.update(self._extract_topics_fast(t.user_message))
        self.context_summary = f"Topics: {','.join(sorted(topics))}"

    def _create_compressed_summary(self, turns: List['ConversationTurn']) -> str:
        """Create a compressed summary of conversation turns."""
        counts = {"cost_analysis": 0, "infrastructure": 0, "pricing_analysis": 0, "ai_services": 0}
        for t in turns:
            for tp in self._extract_topics_fast(t.user_message):
                if tp in counts:
                    counts[tp] += 1
        last_user = turns[-1].user_message[:120].replace("\n", " ") if turns else ""
        last_assistant = turns[-1].assistant_response[:120].replace("\n", " ") if turns else ""
        return f"topics={counts}, last_user='{last_user}...', last_assistant='{last_assistant}...'"

    def compress_old_turns(self, keep_recent: int = 10):
        """Keep only recent turns and carry a compressed summary."""
        if len(self.conversation_history) <= keep_recent:
            return
        old = self.conversation_history[:-keep_recent]
        compressed = self._create_compressed_summary(old)
        self.conversation_history = self.conversation_history[-keep_recent:]
        self.context_summary = f"Previous context: {compressed}; {self.context_summary}"
        logger.info(f"Compressed {len(old)} old turns for session {self.session_id}")

    def _parse_step_data(self, payload: Dict[str, Any], fallback_time: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Parse Bedrock step data with contentBlocks into standardized format."""
        try:
            # payload is expected to be the dict returned under 'payload' from get_invocation_step
            blocks: List[Dict[str, Any]] = payload.get("contentBlocks") or []
            if not blocks:
                return None

            text_content: Optional[str] = None
            role: Optional[str] = None
            ts: Optional[str] = None
            tools_minimal: List[Dict[str, Any]] = []

            for b in blocks:
                if "text" in b:
                    txt = b["text"]
                    if isinstance(txt, str) and txt.startswith("[METADATA]"):
                        try:
                            meta = json.loads(txt[len("[METADATA]"):])
                            role = role or meta.get("role")
                            ts = ts or meta.get("timestamp")
                        except Exception:
                            pass
                    elif isinstance(txt, str) and txt.startswith("[TOOLS]"):
                        try:
                            tdata = json.loads(txt[len("[TOOLS]"):])
                            tlist = tdata.get("tools") or []
                            if isinstance(tlist, list):
                                for t in tlist:
                                    tools_minimal.append({
                                        "tool_name": t.get("tool_name"),
                                        "server_name": t.get("server_name"),
                                        "success": t.get("success"),
                                    })
                        except Exception:
                            pass
                    else:
                        if text_content is None:
                            text_content = txt

            if not ts and fallback_time:
                ts = fallback_time if isinstance(fallback_time, str) else None

            return {
                "role": role,
                "text": text_content or "",
                "timestamp": ts,
                "tools_used": tools_minimal,
            }
        except Exception as e:
            logger.warning(f"Failed to parse step data: {e}")
            return None


class SessionManager:
    """Manages multiple chat sessions with automatic cleanup and Bedrock lifecycle"""

    def __init__(
        self,
        config: Optional[SessionConfig] = None,
        default_session_metadata: Optional[Dict[str, str]] = None,
    ):
        self.config = config or SessionConfig()
        self.config.validate()
        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=self.config.session_timeout_hours)

        # Make Agents Runtime optional at manager level
        try:
            self.backend = BedrockSessionClient(
                region_name=self.config.region_name,
                encryption_key_arn=self.config.encryption_key_arn,
                session_metadata=default_session_metadata,
            )
            logger.info("SessionManager initialized with Bedrock Agent Runtime backend")
        except Exception as e:
            self.backend = None
            logger.warning(f"Bedrock Agent Runtime backend not available; SessionManager will operate in local-only mode: {e}")

        logger.info(f"SessionManager initialized with {self.config.session_timeout_hours}h timeout")

    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get an existing session by ID, return None if not found."""
        return self.sessions.get(session_id)

    def get_or_create_session(self, session_id: Optional[str] = None) -> ChatSession:
        if session_id and session_id in self.sessions:
            session = self.sessions[session_id]
            session.last_activity = datetime.now(timezone.utc)
            return session

        session = ChatSession(
            session_id=session_id,
            bedrock_backend=self.backend,
            config=self.config,
        )
        self.sessions[session.session_id] = session
        return session

    def delete_session(self, session_id: str) -> bool:
        if session_id in self.sessions:
            if self.backend:
                try:
                    self.backend.end_session(session_id)
                    if self.config.delete_on_cleanup:
                        self.backend.delete_session(session_id)
                except ClientError as e:
                    logger.warning(f"Failed to end/delete Bedrock session {session_id}: {e}")
                except Exception as e:
                    logger.warning(f"Error ending/deleting Bedrock session {session_id}: {e}")
            del self.sessions[session_id]
            logger.info(f"Deleted session: {session_id}")
            return True
        return False

    def cleanup_expired_sessions(self) -> int:
        now = datetime.now(timezone.utc)
        expired_sessions = []
        for session_id, session in list(self.sessions.items()):
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            if self.backend:
                try:
                    self.backend.end_session(session_id)
                    if self.config.delete_on_cleanup:
                        self.backend.delete_session(session_id)
                except ClientError as e:
                    logger.warning(f"Failed to end/delete Bedrock session {session_id}: {e}")
                except Exception as e:
                    logger.warning(f"Error ending/deleting Bedrock session {session_id}: {e}")
            del self.sessions[session_id]

        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        return len(expired_sessions)

    def get_all_sessions_stats(self) -> Dict[str, Any]:
        total_sessions = len(self.sessions)
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())
        active_sessions = 0
        now = datetime.now(timezone.utc)
        for session in self.sessions.values():
            if now - session.last_activity < timedelta(minutes=30):
                active_sessions += 1
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_conversation_turns": total_turns,
            "total_tools_executed": total_tools,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600
        }


class SessionHealthCheck:
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager

    def check_bedrock_connectivity(self) -> bool:
        if not self.session_manager.backend:
            return False
        try:
            test_session_id = self.session_manager.backend.create_session()
            self.session_manager.backend.delete_session(test_session_id)
            return True
        except Exception:
            return False

    def get_health_metrics(self) -> Dict[str, Any]:
        stats = self.session_manager.get_all_sessions_stats()
        mem_mb = 0.0
        try:
            import psutil  # optional
            process = psutil.Process()
            mem_mb = process.memory_info().rss / (1024 * 1024)
        except Exception:
            pass
        return {
            **stats,
            "memory_usage_mb": round(mem_mb, 2),
            "bedrock_connectivity": self.check_bedrock_connectivity(),
            "health_status": "healthy" if stats["total_sessions"] < 1000 else "warning"
        }


# Global session manager instance (configure as needed)
session_manager = SessionManager(
    config=SessionConfig(
        region_name=os.getenv("AWS_REGION"),
        encryption_key_arn=None,
        session_timeout_hours=2,
        max_conversation_turns=200,
        max_tools_per_turn=25,
        enable_cleanup=True,
        delete_on_cleanup=False,
        recovery_max_steps=200,
        compress_keep_recent=20,
        max_user_message_len=20000,
        max_assistant_response_len=20000,
        max_metadata_len=5000,
        persist_tools_minimal=True,
    ),
    default_session_metadata={"app": "mcp-bot", "env": "prod"},
)
